# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/github/niucoder

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/github/niucoder/build

# Include any dependencies generated for this target.
include CMakeFiles/bingdukuosan.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/bingdukuosan.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/bingdukuosan.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/bingdukuosan.dir/flags.make

CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o: CMakeFiles/bingdukuosan.dir/flags.make
CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o: /home/<USER>/github/niucoder/bingdukuosan.cpp
CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o: CMakeFiles/bingdukuosan.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o -MF CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o.d -o CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o -c /home/<USER>/github/niucoder/bingdukuosan.cpp

CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/github/niucoder/bingdukuosan.cpp > CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.i

CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/github/niucoder/bingdukuosan.cpp -o CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.s

# Object files for target bingdukuosan
bingdukuosan_OBJECTS = \
"CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o"

# External object files for target bingdukuosan
bingdukuosan_EXTERNAL_OBJECTS =

bingdukuosan: CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o
bingdukuosan: CMakeFiles/bingdukuosan.dir/build.make
bingdukuosan: CMakeFiles/bingdukuosan.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bingdukuosan"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bingdukuosan.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/bingdukuosan.dir/build: bingdukuosan
.PHONY : CMakeFiles/bingdukuosan.dir/build

CMakeFiles/bingdukuosan.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/bingdukuosan.dir/cmake_clean.cmake
.PHONY : CMakeFiles/bingdukuosan.dir/clean

CMakeFiles/bingdukuosan.dir/depend:
	cd /home/<USER>/github/niucoder/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/github/niucoder /home/<USER>/github/niucoder /home/<USER>/github/niucoder/build /home/<USER>/github/niucoder/build /home/<USER>/github/niucoder/build/CMakeFiles/bingdukuosan.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/bingdukuosan.dir/depend

