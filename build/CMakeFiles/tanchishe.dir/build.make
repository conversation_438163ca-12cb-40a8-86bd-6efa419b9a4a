# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/github/niucoder

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/github/niucoder/build

# Include any dependencies generated for this target.
include CMakeFiles/tanchishe.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/tanchishe.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/tanchishe.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/tanchishe.dir/flags.make

CMakeFiles/tanchishe.dir/tanchishe.cpp.o: CMakeFiles/tanchishe.dir/flags.make
CMakeFiles/tanchishe.dir/tanchishe.cpp.o: /home/<USER>/github/niucoder/tanchishe.cpp
CMakeFiles/tanchishe.dir/tanchishe.cpp.o: CMakeFiles/tanchishe.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/tanchishe.dir/tanchishe.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/tanchishe.dir/tanchishe.cpp.o -MF CMakeFiles/tanchishe.dir/tanchishe.cpp.o.d -o CMakeFiles/tanchishe.dir/tanchishe.cpp.o -c /home/<USER>/github/niucoder/tanchishe.cpp

CMakeFiles/tanchishe.dir/tanchishe.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tanchishe.dir/tanchishe.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/github/niucoder/tanchishe.cpp > CMakeFiles/tanchishe.dir/tanchishe.cpp.i

CMakeFiles/tanchishe.dir/tanchishe.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tanchishe.dir/tanchishe.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/github/niucoder/tanchishe.cpp -o CMakeFiles/tanchishe.dir/tanchishe.cpp.s

# Object files for target tanchishe
tanchishe_OBJECTS = \
"CMakeFiles/tanchishe.dir/tanchishe.cpp.o"

# External object files for target tanchishe
tanchishe_EXTERNAL_OBJECTS =

tanchishe: CMakeFiles/tanchishe.dir/tanchishe.cpp.o
tanchishe: CMakeFiles/tanchishe.dir/build.make
tanchishe: CMakeFiles/tanchishe.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/github/niucoder/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable tanchishe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/tanchishe.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/tanchishe.dir/build: tanchishe
.PHONY : CMakeFiles/tanchishe.dir/build

CMakeFiles/tanchishe.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/tanchishe.dir/cmake_clean.cmake
.PHONY : CMakeFiles/tanchishe.dir/clean

CMakeFiles/tanchishe.dir/depend:
	cd /home/<USER>/github/niucoder/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/github/niucoder /home/<USER>/github/niucoder /home/<USER>/github/niucoder/build /home/<USER>/github/niucoder/build /home/<USER>/github/niucoder/build/CMakeFiles/tanchishe.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/tanchishe.dir/depend

