# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/github/niucoder

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/github/niucoder/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles /home/<USER>/github/niucoder/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/github/niucoder/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named HelloWorld

# Build rule for target.
HelloWorld: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 HelloWorld
.PHONY : HelloWorld

# fast build rule for target.
HelloWorld/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/build
.PHONY : HelloWorld/fast

#=============================================================================
# Target rules for targets named tanchishe

# Build rule for target.
tanchishe: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tanchishe
.PHONY : tanchishe

# fast build rule for target.
tanchishe/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/build
.PHONY : tanchishe/fast

#=============================================================================
# Target rules for targets named bingdukuosan

# Build rule for target.
bingdukuosan: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bingdukuosan
.PHONY : bingdukuosan

# fast build rule for target.
bingdukuosan/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bingdukuosan.dir/build.make CMakeFiles/bingdukuosan.dir/build
.PHONY : bingdukuosan/fast

bingdukuosan.o: bingdukuosan.cpp.o
.PHONY : bingdukuosan.o

# target to build an object file
bingdukuosan.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bingdukuosan.dir/build.make CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.o
.PHONY : bingdukuosan.cpp.o

bingdukuosan.i: bingdukuosan.cpp.i
.PHONY : bingdukuosan.i

# target to preprocess a source file
bingdukuosan.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bingdukuosan.dir/build.make CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.i
.PHONY : bingdukuosan.cpp.i

bingdukuosan.s: bingdukuosan.cpp.s
.PHONY : bingdukuosan.s

# target to generate assembly for a file
bingdukuosan.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bingdukuosan.dir/build.make CMakeFiles/bingdukuosan.dir/bingdukuosan.cpp.s
.PHONY : bingdukuosan.cpp.s

tanchishe.o: tanchishe.cpp.o
.PHONY : tanchishe.o

# target to build an object file
tanchishe.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/tanchishe.cpp.o
.PHONY : tanchishe.cpp.o

tanchishe.i: tanchishe.cpp.i
.PHONY : tanchishe.i

# target to preprocess a source file
tanchishe.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/tanchishe.cpp.i
.PHONY : tanchishe.cpp.i

tanchishe.s: tanchishe.cpp.s
.PHONY : tanchishe.s

# target to generate assembly for a file
tanchishe.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tanchishe.dir/build.make CMakeFiles/tanchishe.dir/tanchishe.cpp.s
.PHONY : tanchishe.cpp.s

xiaohongyinzhang.o: xiaohongyinzhang.cpp.o
.PHONY : xiaohongyinzhang.o

# target to build an object file
xiaohongyinzhang.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/xiaohongyinzhang.cpp.o
.PHONY : xiaohongyinzhang.cpp.o

xiaohongyinzhang.i: xiaohongyinzhang.cpp.i
.PHONY : xiaohongyinzhang.i

# target to preprocess a source file
xiaohongyinzhang.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/xiaohongyinzhang.cpp.i
.PHONY : xiaohongyinzhang.cpp.i

xiaohongyinzhang.s: xiaohongyinzhang.cpp.s
.PHONY : xiaohongyinzhang.s

# target to generate assembly for a file
xiaohongyinzhang.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/HelloWorld.dir/build.make CMakeFiles/HelloWorld.dir/xiaohongyinzhang.cpp.s
.PHONY : xiaohongyinzhang.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... HelloWorld"
	@echo "... bingdukuosan"
	@echo "... tanchishe"
	@echo "... bingdukuosan.o"
	@echo "... bingdukuosan.i"
	@echo "... bingdukuosan.s"
	@echo "... tanchishe.o"
	@echo "... tanchishe.i"
	@echo "... tanchishe.s"
	@echo "... xiaohongyinzhang.o"
	@echo "... xiaohongyinzhang.i"
	@echo "... xiaohongyinzhang.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

