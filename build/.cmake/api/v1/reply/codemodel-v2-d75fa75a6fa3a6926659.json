{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "HelloWorld", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "HelloWorld::@6890427a1f51a3e7e1df", "jsonFile": "target-HelloWorld-f7db4ac39e65ff7f62c6.json", "name": "HelloWorld", "projectIndex": 0}, {"directoryIndex": 0, "id": "bingdukuosan::@6890427a1f51a3e7e1df", "jsonFile": "target-bingdukuosan-151f7dab6b33b13a98e8.json", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "projectIndex": 0}, {"directoryIndex": 0, "id": "tanchishe::@6890427a1f51a3e7e1df", "jsonFile": "target-tanchishe-4020f59f330a259b5959.json", "name": "tanchi<PERSON>", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/github/niucoder/build", "source": "/home/<USER>/github/niucoder"}, "version": {"major": 2, "minor": 6}}