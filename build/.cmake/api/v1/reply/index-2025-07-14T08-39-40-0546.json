{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 3, "string": "3.28.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-d75fa75a6fa3a6926659.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-c43c1022b505e8523ffd.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6a70a47d1a25c6562aeb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-ee793ea44d57a62b2395.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-c43c1022b505e8523ffd.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-d75fa75a6fa3a6926659.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-ee793ea44d57a62b2395.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6a70a47d1a25c6562aeb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}