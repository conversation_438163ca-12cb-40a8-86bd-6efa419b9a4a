{"artifacts": [{"path": "HelloWorld"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "id": "HelloWorld::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}], "language": "CXX"}, "name": "HelloWorld", "nameOnDisk": "HelloWorld", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "xiaohongyinzhang.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}