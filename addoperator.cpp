
#include <iostream>
#include <vector>
#include <cmath>
#include <string>
using namespace std;

// 质数检测函数
bool isPrime(int num) {
    if (num <= 1) return false;
    if (num == 2) return true;
    if (num % 2 == 0) return false;
    for (int i = 3; i * i <= num; i += 2) {
        if (num % i == 0) return false;
    }
    return true;
}

// 生成所有可能的表达式并计算总和
void generateExpressions(const string& s, vector<int>& results, int pos, int current, int sum) {
    if (pos == s.length()) {
        sum += current;
        results.push_back(sum);
        return;
    }
    
    // 不插入加号，继续构建当前数字
    generateExpressions(s, results, pos + 1, current * 10 + (s[pos] - '0'), sum);
    
    // 插入加号，将当前数字加到sum中，并重置current
    if (pos != 0) {  // 不在开头插入加号
        generateExpressions(s, results, pos + 1, s[pos] - '0', sum + current);
    }
}

int main() {
    string input;
    cout << "请输入数字字符串: ";
    cin >> input;
    
    vector<int> results;
    generateExpressions(input, results, 0, 0, 0);
    
    cout << "质数结果: ";
    for (int res : results) {
        if (isPrime(res)) {
            cout << res << " ";
        }
    }
    cout << endl;
    
    return 0;
}
