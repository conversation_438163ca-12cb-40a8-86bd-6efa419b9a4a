#include <bits/stdc++.h>
using namespace std;
using ll = long long;

deque<pair<int,int>> snake;

// 

// dir= {1,2,3,4}分别表示上下左右
std::pair<int, int> newHead(int dir, const std::pair<int, int>& old_head) {
    if (dir == 1) {
        return {old_head.first, old_head.second +1}; // 坐标轴在左下角
    } else if (dir == 2) {
        return {old_head.first, old_head.second - 1};
    } else if (dir == 3) {
        return {old_head.first -1, old_head.second};
    } else {
        return {old_head.first + 1, old_head.second};
    }
}

bool moveSnake(int dir) {
    // TODO: 请实现移动逻辑
    auto new_head = newHead(dir, snake.back());
    for (auto it = snake.begin(); it != std::prev(snake.end()); it++) {
        if (*it == new_head) {
            // colllision
            return true;
        }
    }
    snake.push_back(new_head);
    snake.pop_front();
    return false;
}

bool eatSnake(int dir) {
    // TODO: 请实现吃果子生长逻辑
    auto new_head = newHead(dir, snake.back());
    for (auto it = snake.begin(); it != snake.end(); it++) {
        if (*it == new_head) {
            // colllision
            return true;
        }
    }
    snake.push_back(new_head);
    return false;
}



int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    int n, q;
    cin >> n >> q;
    snake.clear();
    for (int i = 0; i < n; i++) {
        int x, y;
        cin >> x >> y;
        snake.emplace_back(x, y);
    }
    for (int i = 0; i < q; i++) {
        int op, dir;
        cin >> op >> dir;
        bool collision = (op == 1 ? moveSnake(dir) : eatSnake(dir));
        if (collision) {
            cout << -1 << '\n';
            return 0;
        } else {
            for (auto it = snake.rbegin(); it != snake.rend(); ++it) {
                cout << it->first << ' ' << it->second << '\n';
            }
        }
    }
    return 0;
}