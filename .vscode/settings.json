{"files.associations": {"random": "cpp", "limits": "cpp", "vector": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "typeinfo": "cpp", "cstring": "cpp", "bitset": "cpp", "charconv": "cpp", "condition_variable": "cpp", "format": "cpp", "mutex": "cpp", "span": "cpp", "variant": "cpp", "__bit_reference": "cpp", "__locale": "cpp", "__split_buffer": "cpp", "__threading_support": "cpp", "__verbose_abort": "cpp", "ios": "cpp", "locale": "cpp", "print": "cpp"}}