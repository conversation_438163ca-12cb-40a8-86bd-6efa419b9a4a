#include <iostream>
#include <string>
#include <vector>
#include <bits/stdc++.h>
using namespace std;



int main() {
    int num;
    cin >> num;

    while(num--) {
        int x, y, t;
        cin >>x >>y >> t;
        std::vector<std::vector<int>> dp(x+1, std::vector(y+1, 0));
        std::vector<std::vector<int>> new_dp = dp;
        dp[0][0] = 1;

        if (t == 0) {
            std::cout << dp[x][y] << std::endl;
        }

        for (int time = 1; time <= t; time++) {
            for (int i = 0; i <= x; i++) {
                for (int j = 0; j <=y; j++) {
                    if (dp[i][j] > 0) {
                        if (i+1<=x) {
                            new_dp[i + 1][j] = dp[i][j] + 1;
                        }
                       if (j+1 <=y) {
                           new_dp[i][j+1] = dp[i][j] + 1;
                       }
                    }
                }
            }
            dp = new_dp;
            new_dp = std::vector<std::vector<int>>(x+1, std::vector(y+1, 0));
        }

        std::cout << dp[x][y] << std::endl;

    }

    return 0;
}