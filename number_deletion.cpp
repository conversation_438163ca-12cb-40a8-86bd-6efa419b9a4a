#include <iostream>
#include <string>
using namespace std;

// int maxDeletions(string num) {
//     int count = 0;
//     while (num.length() > 1) {
//         int sum = 0;
//         for (char c : num) {
//             sum += c - '0';
//         }
//         // sum:所有的数字的总和。
//         bool deleted = false;
//         for (int i = 0; i < num.length(); i++) {
//             int digit = num[i] - '0';
//             if ((sum - digit) % 3 == 0) {
//                 string newNum = num;
//                 newNum.erase(i, 1);
//                 // Remove leading zeros
//                 while (newNum.length() > 1 && newNum[0] == '0') {
//                     newNum.erase(0, 1);
//                 }
//                 if (newNum.length() > 0 && newNum != "0") {
//                     num = newNum;
//                     count++;
//                     deleted = true;
//                     break;
//                 }
//             }
//         }
        
//         if (!deleted) break;
//     }
//     return count;
// }

// int main() {
//     ios::sync_with_stdio(false);
//     cin.tie(nullptr);
    
//     int T;
//     cin >> T;
//     while (T--) {
//         string num;
//         cin >> num;
//         cout << maxDeletions(num) << endl;
//     }
//     return 0;
// }


#include <iostream>
#include <vector>
using namespace std;

int main() {
    int t;
    cin >> t;
    string n;
    while (t--) {
        cin >> n;
        vector<int> vMod(3, 0);//vMod[i]表示原数各位数字中除以3余数为i的个数
        for (char& c : n) {
            ++vMod[(c - '0') % 3];
        }
        int m = (vMod[1] + vMod[2] + vMod[2]) % 3; //原数除以3的余数
        int ans;
        if (m == 0) { //原数为3的倍数
            ans = vMod[0];
            if (ans == n.size()) --ans;
        } else { //原数不是3的倍数
            if (vMod[m] == 0) {   //没有任何一位数字跟原数模3同余
                ans = 0;
            } else {
                ans = vMod[0] + 1; //先删掉同余数字，再删余数为0的
                if (ans == n.size()) --ans;
                if (vMod[m] == 1 && (n[0] - '0') % 3 == m) {
                    for (int i = 1; i < n.size(); ++i) {
                        if (n[i] != '0') break;
                        --ans;
                    }
                }
            }
        }
        cout << ans << endl;
    }
}
