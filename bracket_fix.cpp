#include <iostream>
#include <vector>
#include <string>
#include <climits>
using namespace std;

int minChangesToFix(string s) {
    int n = s.size();
    vector<vector<int>> dp(n, vector<int>(n, 0));
    
    for (int len = 2; len <= n; len += 2) {
        for (int i = 0; i + len - 1 < n; i++) {
            int j = i + len - 1;
            dp[i][j] = INT_MAX;
            
            // Case 1: outer brackets match or can be matched with 0/1 change
            int cost = 0;
            if ((s[i] == '{' && s[j] == '}') || (s[i] == '[' && s[j] == ']')) {
                cost = 0;
            } else if ((s[i] == '{' || s[i] == '[') && (s[j] == '}' || s[j] == ']')) {
                cost = 1;
            } else {
                cost = 2; // both need to change
            }
            dp[i][j] = min(dp[i][j], (i+1 <= j-1 ? dp[i+1][j-1] : 0) + cost);
            
            // Case 2: split into two parts
            for (int k = i + 1; k < j; k += 2) {
                dp[i][j] = min(dp[i][j], dp[i][k] + dp[k+1][j]);
            }
        }
    }
    return dp[0][n-1];
}

int main() {
    int t;
    cin >> t;
    while (t--) {
        string s;
        cin >> s;
        cout << minChangesToFix(s) << endl;
    }
    return 0;
}
