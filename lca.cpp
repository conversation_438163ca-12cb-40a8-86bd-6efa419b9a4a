#include <iostream>
using namespace std;

struct TreeNode {
    int val;
    TreeNode *left;
    TreeNode *right;
    TreeNode(int x) : val(x), left(NULL), right(NULL) {}
};

// 查找二叉树最近公共祖先
TreeNode* lowestCommonAncestor(TreeNode* root, TreeNode* p, TreeNode* q) {
    if (!root || root == p || root == q) return root;
    
    TreeNode* left = lowestCommonAncestor(root->left, p, q);
    TreeNode* right = lowestCommonAncestor(root->right, p, q);
    
    if (left && right) return root;
    return left ? left : right;
}

// 创建测试二叉树
TreeNode* createTestTree() {
    TreeNode* root = new TreeNode(3);
    root->left = new TreeNode(5);
    root->right = new TreeNode(1);
    root->left->left = new TreeNode(6);
    root->left->right = new TreeNode(2);
    root->right->left = new TreeNode(0);
    root->right->right = new TreeNode(8);
    root->left->right->left = new TreeNode(7);
    root->left->right->right = new TreeNode(4);
    return root;
}

int main() {
    TreeNode* root = createTestTree();
    
    // 测试用例1
    TreeNode* p = root->left;  // 5
    TreeNode* q = root->right;  // 1
    TreeNode* lca = lowestCommonAncestor(root, p, q);
    cout << "LCA of 5 and 1 is: " << lca->val << endl;
    
    // 测试用例2
    p = root->left;  // 5
    q = root->left->right->right;  // 4
    lca = lowestCommonAncestor(root, p, q);
    cout << "LCA of 5 and 4 is: " << lca->val << endl;
    
    return 0;
}
