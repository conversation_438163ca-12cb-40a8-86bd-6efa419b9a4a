// // #include <iostream>

// // int main() {
// //     std::cout << "Hello, World!" << std::endl;
// //     return 0;
// // }

// #include <iostream>
// #include <vector>
// #include <algorithm>
// #include <numeric>
// #include <unordered_map>
// #include <unordered_set>
// #include <set>
// #include <stack>
// #include <functional>
// #include <math.h>
// #include <queue>
// #include <deque>
// #include <map>
// #include <thread>
// #include <string.h>
// #include <limits.h>
// using namespace std;
// using ll = long long;
// using pii = pair<int,int>;
// const int MOD = 1e9+7;
  

// //
// // 小美的数组删除
// // 删除第一个元素，同时数组的长度减去1， cost = x,
// // 删除整个数组， cost = k * mex(vec)
// // 返回删除数组的最小cost
// int main()
// {
//     int t;
//     cin>>t;
//     while(t--){
//         int n;
//         cin>>n;
//         ll k,x;
//         cin>>k>>x;
  
//         vector<int> a(n);
//         vector<int> cnt(n+1);
//         for(int i=0;i<n;i++){
//             cin>>a[i];
//             cnt[a[i]]++;
//         }
//         int mex;
//         for(int i=0;i<=n;i++){
//             if(cnt[i]==0){
//                 mex = i;
//                 break;
//             }
//         }
//         ll ans = k*mex;
//         ll cur = 0;
//         for(int i=0;i<n;i++){
//             cur += x;
//             cnt[a[i]]--; // 删除这个元素
//             if(cnt[a[i]]==0){
//                 mex = min(mex,a[i]);
//             }
//             ans = min(ans,cur + k*mex);
//         }
//         cout<<ans<<endl;
//     }
// }


#include <iostream>
#include <string>
#include <vector>
using namespace std;

// 10101010 ==> 100 01010 100 01110
// 101      1
// 1010     1
// 10101    1
// 101010   

//小红得到一个仅由'0'和'1'组成的字符串（01串）。她可以进行任意次操作，每次选择一个字符并将其取反。定义一个01串为好串，当且仅当它不包含"010"和"101"这两个子串。小红想知道，最少需要操作多少次才能将这个字符串变成好串。






// int solution(const std::string& str, int start, int end) {
//     if (end - start <= 2) {
//         return 0;
//     }

//     char a = str[0];
//     char b = str[1];
//     int ans = 0;
//     for (int i =2; i < end; i++) {
//         if (a == '0' && b == '1') {
//             if (str[i] == '0') {
//                 ans++;
//                 a = b;
//                 b = str[i];
//             } else {
//                 a = b;
//                 b = str[i];
//             }
//         } else if (a == '1' && b == '0') {
//             if (str[i] == '1') {
//                 ans++;
//                 a = b;
//                 b = str[i];
//             } else {
//                 a = b;
//                 b = str[i];
//             }
//         } else {
//             a = b;
//             b = str[i];
//         }
//     }

//     return ans;
// }

int main123() {
    std::string str;
    cin >> str;

    int len = str.size();

    // dp[i][0]: 第i为为 0时需要修改的num
    // dp[i][1]: 第i位为1时需要修改的num
    vector<vector<int>> dp(len, vector<int>(2, 0));
    dp[0][0] = (str[0] == '1');
    dp[0][1] = (str[0] == '0');
    for (int i = 1; i < len; i++) {
        if (str[i] == '0') {
            dp[i][0] = dp[i-1][0];
            dp[i][1] = min(dp[i-1][0], dp[i-1][1]) + 1;
        } else {
            dp[i][0] = min(dp[i-1][0], dp[i-1][1]) + 1;
            dp[i][1] = dp[i-1][1];
        }
        std::cout << dp[i][0] << " " << dp[i][1] << std::endl;
    }

    int ret = min(dp[len-1][0], dp[len-1][1]);


    cout << ret << endl;

    return 0;
}
// 64 位输出请用 printf("%lld")


// #include <iostream>
// #include <string>
// #include <vector>
// #include <algorithm>

// int min_operations(const std::string& s) {
//     int n = s.length();
//     std::vector<std::vector<std::vector<int>>> dp(n, std::vector<std::vector<int>>(2, std::vector<int>(2, 0)));
    
//     // 初始化
//     dp[0][0][0] = (s[0] == '0') ? 0 : 1;
//     dp[0][1][0] = (s[0] == '1') ? 0 : 1;
    
//     for (int i = 1; i < n; ++i) {
//         for (int j = 0; j < 2; ++j) {
//             for (int k = 0; k < 2; ++k) {
//                 if (s[i] == '0') {
//                     if (j == 0 && k == 1) {
//                         dp[i][0][0] = std::min(dp[i][0][0], dp[i-1][0][1] + 1);
//                         dp[i][1][0] = std::min(dp[i][1][0], dp[i-1][1][1] + 1);
//                     } else {
//                         dp[i][0][0] = std::min(dp[i][0][0], dp[i-1][0][k]);
//                         dp[i][1][0] = std::min(dp[i][1][0], dp[i-1][1][k]);
//                     }
//                 } else {
//                     if (j == 1 && k == 0) {
//                         dp[i][0][1] = std::min(dp[i][0][1], dp[i-1][0][0] + 1);
//                         dp[i][1][1] = std::min(dp[i][1][1], dp[i-1][1][0] + 1);
//                     } else {
//                         dp[i][0][1] = std::min(dp[i][0][1], dp[i-1][0][k]);
//                         dp[i][1][1] = std::min(dp[i][1][1], dp[i-1][1][k]);
//                     }
//                 }
//             }
//         }
//     }
    
//     int result = std::min(dp[n-1][0][0], dp[n-1][0][1]);
//     result = std::min(result, dp[n-1][1][0]);
//     result = std::min(result, dp[n-1][1][1]);
    
//     return result;
// }

// int main111() {
//     std::string s = "0101";
//     std::cout << "最少操作次数: " << min_operations(s) << std::endl;
//     return 0;
// }

// import java.util.HashMap;
// import java.util.Map;
// import java.util.Scanner;
 
// // 注意类名必须为 Main, 不要有任何 package xxx 信息
// public class Main {
//     public static void main(String[] args) {
//         Scanner in = new Scanner(System.in);
//         //模拟
//         int t=in.nextInt();
//         int[] res=new int[t];
//         Map<Character,int[]> map=new HashMap<>();
//         map.put('W',new int[]{1,1,1,0});
//         map.put('D',new int[]{0,1,1,1});
//         map.put('S',new int[]{1,0,1,1});
//         map.put('A',new int[]{1,1,0,1});
//         map.put('*',new int[]{2,3,4,5});
//         for(int i=0;i<t;i++){
//             int n=in.nextInt();
//             int m=in.nextInt();
//             String[] strs=new String[n];
//             for(int j=0;j<n;j++)
//                 strs[j]=in.next();



//             for(int a=0;a<n;a++){
//                 for(int b=0;b<m;b++){
//                     char c=strs[a].charAt(b);
//                     //左
//                     if(b>0){
//                         if(map.get(c)[0]==map.get(strs[a].charAt(b-1))[2])
//                             res[i]=1;
//                     }
//                     //右
//                     if(b<strs[a].length()-1){
//                         if(map.get(c)[2]==map.get(strs[a].charAt(b+1))[0])
//                             res[i]=1;
//                     }
//                     //上
//                     if(a>0){
//                         if(map.get(c)[1]==map.get(strs[a-1].charAt(b))[3])
//                             res[i]=1;
//                     }
//                     //下
//                     if(a<n-1){
//                         if(map.get(c)[3]==map.get(strs[a+1].charAt(b))[1])
//                             res[i]=1;
//                     }
//                 }
//             }
//         }
//         for(int num:res){
//             if(num==0)
//                 System.out.println("Yes");
//             else
//                 System.out.println("No");
//         }
//     }
// }



// // 横向扫描 + 竖向扫描
// #include <iostream>
// #include <unordered_map>
// #include <unordered_set>
// #include <vector>
// using namespace std;
 
// // 对于每块拼图而言：在各个方向上的合法的搭档
// vector<unordered_set<char>>WLegal = {{'W', '*'}, {'D', 'A', 'W', '*'}, {'A', '*'}, {'D', '*'}};      // {U D L R}
// vector<unordered_set<char>>DLegal = {{'W', '*'}, {'S', '*'}, {'S', 'D', 'W', '*'}, {'D', '*'}};
// vector<unordered_set<char>>SLegal = {{'D', 'S', 'A', '*'}, {'S', '*'}, {'A', '*'}, {'D', '*'}};
// vector<unordered_set<char>>ALegal = {{'W', '*'}, {'S', '*'}, {'A', '*'}, {'W', 'S', 'A', '*'}};
 
// unordered_map<char, vector<unordered_set<char>>>Frame = {{'W', WLegal}, {'D', DLegal}, {'S', SLegal}, {'A', ALegal}};

// int main()
// {
//     int n;
//     cin >> n;
//     int temp_n = n;
//     while (n--)
//     {
//         int row, col;
//         cin >> row >> col;
//         vector<vector<char>> Record(row, vector<char>(col));

//         int index = 0;
//         int temp_row = 0;
//         int temp_col = col;
//         while (row--)
//         {
//             while (temp_col--)
//             {
//                 cin >> Record[temp_row][index++];
//             }
//             index = 0;
//             temp_col = col;
//             temp_row++;
//         }

//         bool flag = true;
//         for (int i = 0; i < Record.size(); i++)
//         {
//             if (flag == false)
//                 break;
//             for (int j = 1; j < Record[i].size(); j += 2)
//             {
//                 if (flag == false)
//                     break;
//                 if (Record[i][j] == '*')
//                     continue;
//                 if (j == Record[i].size() - 1)
//                 {
//                     if (Frame[Record[i][j]][2].find(Record[i][j - 1]) == Frame[Record[i][j]][2].end())
//                         flag = false;
//                 }
//                 else
//                 {
//                     if (Frame[Record[i][j]][2].find(Record[i][j - 1]) ==
//                         Frame[Record[i][j]][2].end())
//                         flag = false;
//                     if (Frame[Record[i][j]][3].find(Record[i][j + 1]) ==
//                         Frame[Record[i][j]][3].end())
//                         flag = false;
//                 }
//             }
//         }

//         if (flag == false)
//         {
//             cout << "No" << endl;
//             continue;
//         }

//         for (int i = 1; i < Record.size(); i += 2)
//         {
//             if (flag == false)
//                 break;
//             for (int j = 0; j < Record[i].size(); j++)
//             {
//                 if (flag == false)
//                     break;
//                 if (Record[i][j] == '*')
//                     continue;
//                 if (i == Record.size() - 1)
//                 {
//                     if (Frame[Record[i][j]][0].find(Record[i - 1][j]) == Frame[Record[i][j]][0].end())
//                         flag = false;
//                 }
//                 else
//                 {
//                     if (Frame[Record[i][j]][0].find(Record[i - 1][j]) == Frame[Record[i][j]][0].end())
//                         flag = false;
//                     if (Frame[Record[i][j]][1].find(Record[i + 1][j]) == Frame[Record[i][j]][1].end())
//                         flag = false;
//                 }
//             }
//         }

//         if (flag == false)
//         {
//             cout << "No" << endl;
//         }
//         else
//         {
//             cout << "Yes" << endl;
//         }
//     }

//     return 0;
// }



int main1231() {
    int num;
    cin >> num;

    std::string str = std::to_string(num);

    int odd = 0;
    int even = 0;
    for (int i = 0; i < str.size(); i++) {
        if ((str[i] - '0') % 2 == 0) {
            even = even * 10 + (str[i] - '0');
        } else {
            odd = odd * 10 + (str[i] - '0');
        }
    }

    // even - odd, stoi(even) - stoi(odd);
    cout << abs(even - odd) << endl;

    return 0;
}

// 整数切割
int main() {
    int num;
    cin >> num;

    std::string str = std::to_string(num);

    int odd = 0;
    int even = 0;
    for (int i = 0; i < str.size(); i++) {
        if ((str[i] - '0') % 2 == 0) {
            even = even * 10 + (str[i] - '0');
        } else {
            odd = odd * 10 + (str[i] - '0');
        }
    }

    // even - odd, stoi(even) - stoi(odd);
    cout << abs(even - odd) << endl;

    return 0;
}



int main1231() {
    std::string num;
    cin >>num;
    int ans = 0;
    int len = num.size();
    bool right_even = (num[len-1] - '0') % 2 == 0;

    for (int index = 0; index < num.size() -1; index++) {
        // std::string left = num.substr(0, index);
        // std::string right = num.substr(index);
        // // left + right is odd, 
        // int left_num = std::stoi(left);
        // int right_num = std::stoi(right);
        // if (left_num + right_num)) {
        //     ans++;
        // }
        if (right_even && (num[index] - '0') % 2 == 0 ) {
            ans++;
            continue;
        }

        if (!right_even && (num[index] - '0') % 2 == 1) {
            ans++;
        }


    }

    std::cout << ans << std::endl;
    return 0;
}

int main() {
    int station_num;
    std::vector<int> dis_vec;
    int x,y;
    cin >> station_num;
    dis_vec.resize(station_num);
    for (int i =0; i < station_num; i++) {
        cin >> dis_vec[i];
    }
    cin >>x >>y;
    // std::cout <<x << " " << y << std::endl;

    // 正向走。
    int total_dis = 0;
    if (x <= y) {
        int dis = 0;
        for (int i = x; i <y; i++) {
            dis += dis_vec[i-1];
        }

        int rev_dis = 0;

        for (int i = y-1; i < station_num; i++) {
            rev_dis += dis_vec[i];
        }
        for (int i =0; i < x -1; i++) {
            rev_dis += dis_vec[i];
        }

        total_dis = std::max(dis, rev_dis);
    } else {
        int dis = 0;
        for (int i = y; i < x; i++)
        {
            dis += dis_vec[i - 1];
        }

        int rev_dis = 0;

        for (int i = x - 1; i < station_num; y++)
        {
            rev_dis += dis_vec[i];
        }
        for (int i = 0; i < y - 1; i++)
        {
            rev_dis += dis_vec[i];
        }

        total_dis = std::max(dis, rev_dis);
    }

    std::cout << total_dis << std::endl;

}