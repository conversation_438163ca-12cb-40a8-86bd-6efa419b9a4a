#include <bits/stdc++.h>
using namespace std;
int n, m, k;
const int M = 505;
char mp[M][M];

int main()
{
    ios::sync_with_stdio(false), cin.tie(0), cout.tie(0);
    cin >> n >> m >> k;
    for (int i = 1; i <= n+1; i++)
    {
        for (int j = 1; j <= m + 1; j++)
        {
            mp[i][j] = '.';
        }
    }
    for (int i = 1; i <= k; i++)
    {
        int x, y;
        char c;
        cin >> x >> y >> c;
        x++;
        y++; // 预留2行2列
        mp[x][y] = c;
        mp[x + 1][y] = c;
        mp[x + 2][y] = c;
        mp[x - 1][y] = c;
        mp[x - 2][y] = c;
        mp[x][y + 1] = c;
        mp[x][y + 2] = c;
        mp[x][y - 1] = c;
        mp[x][y - 2] = c;
    }
    for (int i = 2; i <= n +1; i++)
    {
        for (int j = 2; j <= m +1; j++)
        {
            cout << mp[i][j];
        }
        cout << '\n';
    }
    std::cout << std::endl;
    return 0;
}